<template>
  <div class="stock-import-component">
    <!-- Sub Header -->
    <SubHeaderV2Component
      :title="$t('StockManagement.nhap_kho')"
      :show-back="true"
      @back="$router.back()"
    />

    <!-- Header -->
    <div class="component-header">
      <!-- <h1>{{ $t('StockManagement.nhap_kho') }}</h1> -->
      <p>{{ $t('StockManagement.ghi_nhan_hang_nhap') }}</p>
    </div>

    <!-- Import Form -->
    <form @submit.prevent="submitImport" class="import-form">
      <!-- Product Selection -->
      <div class="form-group">
        <label class="form-label">{{ $t('StockManagement.chon_san_pham') }} *</label>
        <div class="product-selector" @click="showProductPicker = true">
          <div v-if="selectedProduct" class="selected-product">
            <img :src="selectedProduct.image || '/default-product.png'" :alt="selectedProduct.name" class="product-image">
            <div class="product-info">
              <h4>{{ selectedProduct.name }}</h4>
              <p>{{ selectedProduct.category }}</p>
            </div>
          </div>
          <div v-else class="placeholder">
            <Icon name="solar:box-bold" size="24" />
            <span>{{ $t('StockManagement.chon_san_pham') }}</span>
          </div>
          <Icon name="solar:arrow-right-bold" size="20" class="arrow-icon" />
        </div>
      </div>

      <!-- Quantity -->
      <div class="form-group">
        <label class="form-label">{{ $t('StockManagement.so_luong') }} *</label>
        <div class="quantity-input">
          <input
            v-model.number="formData.quantity"
            type="number"
            :placeholder="$t('StockManagement.nhap_so_luong')"
            min="1"
            step="1"
            required
            class="form-input"
          />
          <span class="unit-label">{{ selectedProduct?.unit || $t('StockManagement.don_vi') }}</span>
        </div>
      </div>

      <!-- Purchase Price -->
      <div class="form-group">
        <label class="form-label">{{ $t('StockManagement.gia_nhap') }} *</label>
        <div class="price-input">
          <input
            v-model.number="formData.purchase_price"
            type="number"
            :placeholder="$t('StockManagement.nhap_gia')"
            min="0"
            step="1000"
            required
            class="form-input"
          />
          <span class="currency-label">VND</span>
        </div>
      </div>

      <!-- Supplier Info -->
      <div class="form-group">
        <label class="form-label">{{ $t('StockManagement.nha_cung_cap') }}</label>
        <input
          v-model="formData.supplier_info"
          type="text"
          :placeholder="$t('StockManagement.nha_cung_cap')"
          class="form-input"
        />
      </div>

      <!-- Receipt Image Upload -->
      <div class="form-group">
        <label class="form-label">{{ $t('StockManagement.bien_lai_nhap_hang') }}</label>
        <div class="image-upload" @click="triggerImageUpload">
          <input
            ref="imageInput"
            type="file"
            accept="image/*"
            @change="handleImageUpload"
            style="display: none"
          />
          <div v-if="formData.receipt_image" class="uploaded-image">
            <img :src="formData.receipt_image" alt="Receipt" />
            <button type="button" @click.stop="removeImage" class="remove-image-btn">
              <Icon name="solar:trash-bin-trash-bold" size="16" />
            </button>
          </div>
          <div v-else class="upload-placeholder">
            <Icon name="solar:camera-bold" size="32" />
            <p>{{ $t('StockManagement.tai_len_hinh') }}</p>
            <span>{{ $t('StockManagement.chup_hinh') }} / {{ $t('StockManagement.chon_tu_thu_vien') }}</span>
          </div>
        </div>
      </div>

      <!-- Notes -->
      <div class="form-group">
        <label class="form-label">{{ $t('StockManagement.ghi_chu') }}</label>
        <textarea
          v-model="formData.notes"
          :placeholder="$t('StockManagement.them_ghi_chu')"
          rows="3"
          class="form-textarea"
        ></textarea>
      </div>

      <!-- Action Buttons -->
      <div class="form-actions">
        <button type="button" @click="$router.back()" class="btn btn-secondary">
          {{ $t('StockManagement.huy_bo') }}
        </button>
        <button type="submit" :disabled="!isFormValid || loading" class="btn btn-primary">
          <Icon v-if="loading" name="solar:refresh-bold" size="20" class="loading-icon" />
          {{ $t('StockManagement.luu_thong_tin') }}
        </button>
      </div>
    </form>

    <!-- Product Picker Modal -->
    <ProductPickerModal
      v-if="showProductPicker"
      :shop-id="shopId"
      @close="showProductPicker = false"
      @select="selectProduct"
    />

    <!-- Success Toast -->
    <div v-if="showSuccessToast" class="success-toast">
      <Icon name="solar:check-circle-bold" size="24" />
      <span>{{ $t('StockManagement.cap_nhat_thanh_cong') }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { StockService } from '~/services/stockService/stockService'
import { MqttService } from '~/services/mqttService/mqttService'
import ProductPickerModal from './ProductPickerModal.vue'
import SubHeaderV2Component from '~/components/header/SubHeaderV2Component.vue'
import { HttpStatusCode } from "axios";
// Props
const props = defineProps<{
  mode?: string
}>()

// Composables
const router = useRouter()
const route = useRoute()
const { t } = useI18n()

// Use the centralized shop data composable
const {
  activeShop,
  isLoading: shopDataLoading,
  initializeShopData,
  getShopBySlug
} = useShopData()

// Services
const stockService = new StockService()
const mqttService = new MqttService()

// Reactive data
const loading = ref(false)
const showProductPicker = ref(false)
const showSuccessToast = ref(false)
const selectedProduct = ref(null)
const imageInput = ref(null)

const formData = ref({
  product_id: '',
  quantity: null,
  purchase_price: null,
  supplier_info: '',
  receipt_image: '',
  notes: ''
})

// Computed properties
const isFormValid = computed(() => {
  return selectedProduct.value && 
         formData.value.quantity > 0 && 
         formData.value.purchase_price > 0
})

const shopId = computed(() => {
  // For agent mode, get shop ID from route params
  if (props.mode === 'agent') {
    const shopSlug = route.params.id as string
    const shop = getShopBySlug(shopSlug)
    return shop?.id || shopSlug
  }

  // For my_shop mode, use active shop or user's own shop
  return activeShop.value?.id || '1'
})

// Methods
const selectProduct = (product: any) => {
  selectedProduct.value = product
  formData.value.product_id = product.id
  showProductPicker.value = false
}

const triggerImageUpload = () => {
  imageInput.value?.click()
}

const handleImageUpload = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert(t('StockManagement.dung_luong_anh_toi_da', { size: '5' }))
      return
    }

    // Convert to base64 for preview
    const reader = new FileReader()
    reader.onload = (e) => {
      formData.value.receipt_image = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}

const removeImage = () => {
  formData.value.receipt_image = ''
  if (imageInput.value) {
    imageInput.value.value = ''
  }
}

const submitImport = async () => {
  if (!isFormValid.value) return

  loading.value = true
  try {
    const importData = {
      ...formData.value,
      shop_id: shopId.value
    }

    const response = await stockService.stockImport(importData)
    console
    if (response.status == HttpStatusCode.Ok) {
      // Show success message
      showSuccessToast.value = true
      setTimeout(() => {
        showSuccessToast.value = false
      }, 3000)

      // Publish MQTT update
      mqttService.publish(`stock_updates_${shopId.value}`, {
        type: 'stock_import_complete',
        data: {
          product_id: formData.value.product_id,
          quantity: formData.value.quantity,
          timestamp: new Date().toISOString()
        }
      })

      // Reset form
      resetForm()
    } else {
      throw new Error(response.message || 'Import failed')
    }
  } catch (error) {
    console.error('Stock import error:', error)
    alert(t('StockManagement.loi_cap_nhat'))
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  selectedProduct.value = null
  formData.value = {
    product_id: '',
    quantity: null,
    purchase_price: null,
    supplier_info: '',
    receipt_image: '',
    notes: ''
  }
  if (imageInput.value) {
    imageInput.value.value = ''
  }
}

// Lifecycle
onMounted(async () => {
  // Initialize shop data first
  await initializeShopData()
})
</script>

<style scoped>
.stock-import-component {
  padding: 15px;
  background-color: #f8f9fa;
  min-height: 100vh;
  width: 100%;
  max-width: 750px;
}

.component-header {
  margin-bottom: 20px;
  text-align: center;

  h1 {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
  }

  p {
    font-size: 14px;
    color: #7f8c8d;
    margin: 0;
  }
}

.import-form {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.product-selector {
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: border-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:hover {
    border-color: #3498db;
  }

  .selected-product {
    display: flex;
    align-items: center;
    gap: 12px;

    .product-image {
      width: 40px;
      height: 40px;
      border-radius: 6px;
      object-fit: cover;
    }

    .product-info {
      h4 {
        font-size: 14px;
        font-weight: 600;
        margin: 0 0 4px 0;
        color: #2c3e50;
      }

      p {
        font-size: 12px;
        color: #7f8c8d;
        margin: 0;
      }
    }
  }

  .placeholder {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #7f8c8d;

    span {
      font-size: 14px;
    }
  }

  .arrow-icon {
    color: #bdc3c7;
  }
}

.quantity-input,
.price-input {
  display: flex;
  align-items: center;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  overflow: hidden;

  .form-input {
    flex: 1;
    border: none;
    padding: 12px 15px;
    font-size: 14px;
    outline: none;

    &:focus {
      background-color: #f8f9fa;
    }
  }

  .unit-label,
  .currency-label {
    background-color: #ecf0f1;
    padding: 12px 15px;
    font-size: 14px;
    font-weight: 600;
    color: #7f8c8d;
  }
}

.form-input {
  width: 100%;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  padding: 12px 15px;
  font-size: 14px;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #3498db;
    background-color: #f8f9fa;
  }
}

.form-textarea {
  width: 100%;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  padding: 12px 15px;
  font-size: 14px;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #3498db;
    background-color: #f8f9fa;
  }
}

.image-upload {
  border: 2px dashed #ecf0f1;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s ease;

  &:hover {
    border-color: #3498db;
  }

  .uploaded-image {
    position: relative;
    display: inline-block;

    img {
      max-width: 200px;
      max-height: 150px;
      border-radius: 6px;
    }

    .remove-image-btn {
      position: absolute;
      top: -8px;
      right: -8px;
      background: #e74c3c;
      color: white;
      border: none;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }

  .upload-placeholder {
    color: #7f8c8d;

    p {
      font-size: 16px;
      font-weight: 600;
      margin: 10px 0 5px 0;
    }

    span {
      font-size: 12px;
    }
  }
}

.form-actions {
  display: flex;
  gap: 15px;
  margin-top: 30px;

  .btn {
    flex: 1;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    &.btn-secondary {
      background: #ecf0f1;
      color: #7f8c8d;
      border: none;

      &:hover {
        background: #d5dbdb;
      }
    }

    &.btn-primary {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
      border: none;

      &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}

.loading-icon {
  animation: spin 1s linear infinite;
}

.success-toast {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #27ae60;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1000;
  animation: slideDown 0.3s ease;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@media (max-width: 480px) {
  .stock-import-component {
    padding: 10px;
  }

  .import-form {
    padding: 15px;
  }

  .form-actions {
    flex-direction: column;
  }
}
</style>
